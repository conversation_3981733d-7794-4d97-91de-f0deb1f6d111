// Main JavaScript file for PornTubeX
class PornTubeX {
    constructor() {
        this.videos = [];
        this.users = [];
        this.comments = [];
        this.categories = [];
        this.favorites = this.getFavorites();
        this.likes = this.getLikes();

        this.init();
    }
    
    async init() {
        try {
            console.log('PornTubeX: Starting initialization...');
            await this.loadData();
            console.log('PornTubeX: Data loaded, videos count:', this.videos.length);
            this.setupEventListeners();
            console.log('PornTubeX: Initialization complete');
        } catch (error) {
            console.error('Failed to initialize PornTubeX:', error);
            this.showError('Failed to load content. Please refresh the page.');
        }
    }
    
    async loadData() {
        try {
            // Load new TypeScript data first (always available)
            await this.loadNewMediaData();

            // Try to load original data files
            try {
                const [videosResponse, usersResponse, commentsResponse, categoriesResponse] = await Promise.all([
                    fetch('data/videos.json'),
                    fetch('data/users.json'),
                    fetch('data/comments.json'),
                    fetch('data/categories.json')
                ]);

                if (videosResponse.ok && usersResponse.ok && commentsResponse.ok && categoriesResponse.ok) {
                    const videosData = await videosResponse.json();
                    const usersData = await usersResponse.json();
                    const commentsData = await commentsResponse.json();
                    const categoriesData = await categoriesResponse.json();

                    // Combine original and new data
                    this.videos = [...videosData.videos, ...this.newVideos];
                    this.users = usersData.users;
                    this.comments = commentsData.comments;
                    this.categories = [...categoriesData.categories, ...this.newCategories];
                } else {
                    throw new Error('Some data files not found');
                }
            } catch (originalDataError) {
                console.warn('Original data files not available, using new data only:', originalDataError);
                // Use only new data if original files fail
                this.videos = this.newVideos;
                this.users = this.getDefaultUsers();
                this.comments = [];
                this.categories = this.newCategories;
            }

        } catch (error) {
            console.error('Error loading data:', error);
            // Provide minimal fallback data
            this.videos = this.newVideos || [];
            this.users = this.getDefaultUsers();
            this.comments = [];
            this.categories = this.newCategories || [];
        }
    }

    async loadNewMediaData() {
        try {
            console.log('PornTubeX: Loading embedded media data...');
            // Load our new video and photo data directly (embedded)
            this.newVideos = this.getEmbeddedVideoData();
            this.newPhotos = this.getEmbeddedPhotoData();
            console.log('PornTubeX: Loaded', this.newVideos.length, 'videos and', this.newPhotos.length, 'photos');

            // Create new categories for our content
            this.newCategories = [
                {
                    id: "new_videos",
                    name: "New Videos",
                    description: "Latest video content from categories",
                    icon: "🎬",
                    subcategories: [
                        { id: "threesome", name: "Threesome", description: "Threesome content" },
                        { id: "stepsister", name: "Step Sister", description: "Step sister content" },
                        { id: "lesbian", name: "Lesbian", description: "Lesbian content" }
                    ]
                },
                {
                    id: "photos",
                    name: "Photo Gallery",
                    description: "High quality photo collection",
                    icon: "📸",
                    subcategories: [
                        { id: "gallery", name: "Gallery", description: "Photo gallery" }
                    ]
                }
            ];

        } catch (error) {
            console.error('Error loading new media data:', error);
            this.newVideos = [];
            this.newPhotos = [];
            this.newCategories = [];
        }
    }

    getEmbeddedVideoData() {
        // All 50 videos from the categories - embedded directly
        return [
            {
                id: "v_1",
                title: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                description: "ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins",
                thumbnail: "categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg",
                videoUrl: "categories/Videos/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4",
                duration: "25:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_1", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/1.jpg" },
                tags: ["threesome", "adult", "angela white"],
                category: "new_videos",
                subcategory: "threesome",
                views: 45230,
                likes: 1250,
                rating: 4.7,
                formats: { mp4: true }
            },
            {
                id: "v_2",
                title: "OH FUCK! I want to Cum all over your Cock!",
                description: "OH FUCK! I want to Cum all over your Cock!",
                thumbnail: "categories/Thumbnails/OH FUCK! I want to Cum all over your Cock! - Pornhub.com.jpg",
                videoUrl: "categories/Videos/OH FUCK! I want to Cum all over your Cock! - Pornhub.com.ts",
                duration: "18:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_2", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/2.jpg" },
                tags: ["adult", "hardcore"],
                category: "new_videos",
                subcategory: "general",
                views: 32100,
                likes: 890,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_3",
                title: "Cum inside Me not so Innocent StepSis Charli O",
                description: "Cum inside Me not so Innocent StepSis Charli O Fucks and Takes Condom off",
                thumbnail: "categories/Thumbnails/Cum inside Me' not so Innocent StepSis Charli O Fucks and Takes Condom off - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Cum inside Me' not so Innocent StepSis Charli O Fucks and Takes Condom off - Pornhub.com.mp4",
                duration: "22:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_3", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/3.jpg" },
                tags: ["stepsister", "adult", "family"],
                category: "new_videos",
                subcategory: "stepsister",
                views: 28750,
                likes: 765,
                rating: 4.6,
                formats: { mp4: true }
            },
            {
                id: "v_4",
                title: "My Stepmother Discovers how Inexperienced I am",
                description: "My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me",
                thumbnail: "categories/Thumbnails/My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me. - Pornhub.com_2.jpg",
                videoUrl: "/categories/Videos/My Stepmother Discovers how Inexperienced I am and Volunteers to Teach Me. - Pornhub.com_2.ts",
                duration: "30:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_4", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/4.jpg" },
                tags: ["stepmother", "adult", "family"],
                category: "new_videos",
                subcategory: "family",
                views: 41200,
                likes: 1120,
                rating: 4.8,
                formats: { ts: true }
            },
            {
                id: "v_5",
                title: "2 Lesbians 1 Arab",
                description: "2 Lesbians 1 Arab - Hot lesbian action",
                thumbnail: "categories/Thumbnails/2 Lesbians 1 Arab - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/2 Lesbians 1 Arab - Pornhub.com.mp4",
                duration: "19:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_5", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/5.jpg" },
                tags: ["lesbian", "adult", "arab"],
                category: "new_videos",
                subcategory: "lesbian",
                views: 35600,
                likes: 980,
                rating: 4.4,
                formats: { mp4: true }
            },
            {
                id: "v_6",
                title: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                description: "BANGBROS - Mia Khalifa Plays with her Gorgeous Tits",
                thumbnail: "categories/Thumbnails/BANGBROS - Mia Khalifa Plays with her Gorgoeus Tits until Sean Lawless comes and Fuck her - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/BANGBROS - Mia Khalifa Plays with her Gorgoeus Tits until Sean Lawless comes and Fuck her - Pornhub.com.mp4",
                duration: "28:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_6", username: "BangBros", avatar: "https://randomuser.me/api/portraits/lego/6.jpg" },
                tags: ["adult", "mia khalifa", "bangbros"],
                category: "new_videos",
                subcategory: "general",
                views: 52300,
                likes: 1450,
                rating: 4.9,
                formats: { mp4: true }
            },
            {
                id: "v_7",
                title: "BLACKED Curvy Latina Dominated By BBC",
                description: "BLACKED Curvy Latina Dominated By BBC",
                thumbnail: "/categories/Thumbnails/BLACKED Curvy Latina Dominated By BBC.jpg",
                videoUrl: "/categories/Videos/BLACKED Curvy Latina Dominated By BBC.mp4",
                duration: "32:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_7", username: "Blacked", avatar: "https://randomuser.me/api/portraits/lego/7.jpg" },
                tags: ["adult", "latina", "bbc", "blacked"],
                category: "new_videos",
                subcategory: "general",
                views: 48900,
                likes: 1320,
                rating: 4.8,
                formats: { mp4: true }
            },
            {
                id: "v_8",
                title: "Big Ass Big Tit Pakistani Neighbor Fucks Hard",
                description: "Big Ass Big Tit Pakistani Neighbor Fucks Hard",
                thumbnail: "categories/Thumbnails/Big Ass Big Tit Pakistani Neighbor Fucks Hard - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Big Ass Big Tit Pakistani Neighbor Fucks Hard - Pornhub.com.mp4",
                duration: "24:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_8", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/8.jpg" },
                tags: ["adult", "pakistani", "neighbor", "big ass"],
                category: "new_videos",
                subcategory: "general",
                views: 35400,
                likes: 890,
                rating: 4.5,
                formats: { mp4: true }
            },
            {
                id: "v_9",
                title: "Cheated on my Wife with my Neighbor",
                description: "Cheated on my Wife with my Neighbor",
                thumbnail: "categories/Thumbnails/Cheated on my Wife with my Neighbor. - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Cheated on my Wife with my Neighbor. - Pornhub.com.mp4",
                duration: "21:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_9", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/9.jpg" },
                tags: ["adult", "neighbor", "cheating", "wife"],
                category: "new_videos",
                subcategory: "general",
                views: 29800,
                likes: 720,
                rating: 4.3,
                formats: { mp4: true }
            },
            {
                id: "v_10",
                title: "Cunning Stepson Excited his Stepmom using an App",
                description: "Cunning Stepson Excited his Stepmom using an App-SEX ACTIONS!!",
                thumbnail: "categories/Thumbnails/Cunning Stepson Excited his Stepmom using an App-SEX ACTIONS!! Bitch Washed her Face with Sperm. - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Cunning Stepson Excited his Stepmom using an App-SEX ACTIONS!! Bitch Washed her Face with Sperm. - Pornhub.com.mp4",
                duration: "26:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_10", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/10.jpg" },
                tags: ["adult", "stepson", "stepmom", "family"],
                category: "new_videos",
                subcategory: "family",
                views: 38200,
                likes: 950,
                rating: 4.6,
                formats: { mp4: true }
            },
            {
                id: "v_11",
                title: "Dane Jones all Natural Asian Girl Aaeysha Takes Big Dick",
                description: "Dane Jones all Natural Asian Girl Aaeysha Takes Big Dick in Hairy Pussy",
                thumbnail: "categories/Thumbnails/Dane Jones all Natural Asian Girl Aaeysha Takes Big Dick in Hairy Pussy - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Dane Jones all Natural Asian Girl Aaeysha Takes Big Dick in Hairy Pussy - Pornhub.com.mp4",
                duration: "23:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_11", username: "DaneJones", avatar: "https://randomuser.me/api/portraits/lego/11.jpg" },
                tags: ["adult", "asian", "natural", "dane jones"],
                category: "new_videos",
                subcategory: "asian",
                views: 42100,
                likes: 1180,
                rating: 4.7,
                formats: { mp4: true }
            },
            {
                id: "v_12",
                title: "Egyptian Sharmota 2025",
                description: "Egyptian Sharmota 2025 اوسخ سكس مصرى رباب الشرموطة",
                thumbnail: "categories/Thumbnails/Egyptian Sharmota 2025 اوسخ سكس مصرى رباب الشرموطة طيزها نار بتقوله احضنى جامد من ورا احووو على شخرت - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Egyptian Sharmota 2025 اوسخ سكس مصرى رباب الشرموطة طيزها نار بتقوله احضنى جامد من ورا احووو على شخرت - Pornhub.com.mp4",
                duration: "19:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_12", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/12.jpg" },
                tags: ["adult", "egyptian", "arabic", "middle eastern"],
                category: "new_videos",
                subcategory: "ethnic",
                views: 31500,
                likes: 780,
                rating: 4.4,
                formats: { mp4: true }
            },
            {
                id: "v_13",
                title: "First Big Dick for Japanese Teen",
                description: "First Big Dick for Japanese Teen",
                thumbnail: "categories/Thumbnails/First Big Dick for Japanese Teen - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/First Big Dick for Japanese Teen - Pornhub.com.mp4",
                duration: "20:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_13", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/13.jpg" },
                tags: ["adult", "japanese", "teen", "asian"],
                category: "new_videos",
                subcategory: "asian",
                views: 36700,
                likes: 920,
                rating: 4.5,
                formats: { mp4: true }
            },
            {
                id: "v_14",
                title: "Fucking IN the CAR",
                description: "Fucking IN the CAR - پسر 22 ساله مخ زن 40 ساله رو زد",
                thumbnail: "categories/Thumbnails/Fucking IN the CAR - پسر 22 ساله مخ زن 40 ساله رو زد و بردش تو باغ گا.ییدش - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Fucking IN the CAR - پسر 22 ساله مخ زن 40 ساله رو زد و بردش تو باغ گا.ییدش - Pornhub.com.mp4",
                duration: "17:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_14", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/14.jpg" },
                tags: ["adult", "car", "persian", "public"],
                category: "new_videos",
                subcategory: "public",
                views: 28900,
                likes: 650,
                rating: 4.2,
                formats: { mp4: true }
            },
            {
                id: "v_15",
                title: "Angela White's Beautiful & Bountiful Breasts - FULL LENGTH",
                description: "Full Video - FULL LENGTH - Angela White's Beautiful & Bountiful Breasts -EXTRA LONG",
                thumbnail: "categories/Thumbnails/Full Video - FULL LENGTH - Angela White's Beautiful & Bountiful Breasts -EXTRA LONG.jpg",
                videoUrl: "/categories/Videos/Full Video - FULL LENGTH - Angela White's Beautiful & Bountiful Breasts -EXTRA LONG.mp4",
                duration: "45:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_15", username: "AngelaWhite", avatar: "https://randomuser.me/api/portraits/lego/15.jpg" },
                tags: ["adult", "angela white", "full length", "big tits"],
                category: "new_videos",
                subcategory: "pornstar",
                views: 67800,
                likes: 2150,
                rating: 4.9,
                formats: { mp4: true }
            },
            {
                id: "v_16",
                title: "PropertySex Exotic Real Estate Agent With Huge Natural Tits",
                description: "PropertySex Exotic Real Estate Agent With Huge Natural Tits Bangs Her New Client",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Full Video - PropertySex Exotic Real Estate Agent With Huge Natural Tits Bangs Her New Client.ts",
                duration: "35:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_16", username: "PropertySex", avatar: "https://randomuser.me/api/portraits/lego/16.jpg" },
                tags: ["adult", "real estate", "natural tits", "roleplay"],
                category: "new_videos",
                subcategory: "roleplay",
                views: 44300,
                likes: 1280,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_17",
                title: "PropertySex Horny Agent with Huge Natural Tits Makes Sex Video",
                description: "PropertySex Horny Agent with Huge Natural Tits Makes Sex Video with Homebuyer",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Full Video - PropertySex Horny Agent with Huge Natural Tits Makes Sex Video with Homebuyer.ts",
                duration: "33:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_17", username: "PropertySex", avatar: "https://randomuser.me/api/portraits/lego/17.jpg" },
                tags: ["adult", "real estate", "agent", "roleplay"],
                category: "new_videos",
                subcategory: "roleplay",
                views: 39800,
                likes: 1150,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_18",
                title: "I CREAMPIED my Bestie from Bangladesh",
                description: "I CREAMPIED my Bestie from Bangladesh 🇧🇩",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/I CREAMPIED my Bestie from Bangladesh 🇧🇩 - Pornhub.com.ts",
                duration: "22:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_18", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/18.jpg" },
                tags: ["adult", "bangladesh", "creampie", "asian"],
                category: "new_videos",
                subcategory: "asian",
                views: 33200,
                likes: 850,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_19",
                title: "I've been Hinting to you all Day! Well, Fuck me Already!",
                description: "I've been Hinting to you all Day! Well, Fuck me Already! - Dolly Rud",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/I've been Hinting to you all Day! Well, Fuck me Already! - Dolly Rud - Pornhub.com.ts",
                duration: "24:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_19", username: "DollyRud", avatar: "https://randomuser.me/api/portraits/lego/19.jpg" },
                tags: ["adult", "dolly rud", "seduction"],
                category: "new_videos",
                subcategory: "general",
                views: 41700,
                likes: 1090,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_20",
                title: "Indian Village Romantic Sex with Desi Girlfriend",
                description: "Indian Village Romantic Sex with Desi Girlfriend Full Hindi Video",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Indian Village Romantic Sex with Desi Girlfriend Full Hindi Video - Pornhub.com.ts",
                duration: "27:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_20", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/20.jpg" },
                tags: ["adult", "indian", "desi", "romantic", "village"],
                category: "new_videos",
                subcategory: "romantic",
                views: 38900,
                likes: 980,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_21",
                title: "Iraqi Cheating Wife got CREAMPIED",
                description: "Iraqi Cheating Wife got CREAMPIED",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Iraqi Cheating Wife got CREAMPIED - Pornhub.com.ts",
                duration: "19:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_21", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/21.jpg" },
                tags: ["adult", "iraqi", "cheating", "wife", "creampie"],
                category: "new_videos",
                subcategory: "ethnic",
                views: 29500,
                likes: 720,
                rating: 4.3,
                formats: { ts: true }
            },
            {
                id: "v_22",
                title: "LEZ BE BAD - Oiled up GFs Sarah Arabic & little Puck ORGASM",
                description: "LEZ BE BAD - Oiled up GFs Sarah Arabic & little Puck ORGASM during ROUGH LESBIAN SCISSORING SESH!",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/LEZ BE BAD - Oiled up GFs Sarah Arabic & little Puck ORGASM during ROUGH LESBIAN SCISSORING SESH! - Pornhub.com_2.ts",
                duration: "31:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_22", username: "LezBeBad", avatar: "https://randomuser.me/api/portraits/lego/22.jpg" },
                tags: ["adult", "lesbian", "oiled", "scissoring", "orgasm"],
                category: "new_videos",
                subcategory: "lesbian",
                views: 52800,
                likes: 1650,
                rating: 4.8,
                formats: { ts: true }
            },
            {
                id: "v_23",
                title: "Little StepSister SNEAKS into my Room... FORGOT TO WEAR PANTIES",
                description: "Little StepSister SNEAKS into my Room... FORGOT TO WEAR PANTIES",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Little StepSister SNEAKS into my Room... FORGOT TO WEAR PANTIES - Pornhub.com.ts",
                duration: "23:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_23", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/23.jpg" },
                tags: ["adult", "stepsister", "panties", "family"],
                category: "new_videos",
                subcategory: "stepsister",
                views: 43200,
                likes: 1180,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_24",
                title: "MY STEPSISTER HELPS ME LOSE MY VIRGINITY",
                description: "MY STEPSISTER HELPS ME LOSE MY VIRGINITY",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/MY STEPSISTER HELPS ME LOSE MY VIRGINITY - Pornhub.com.ts",
                duration: "26:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_24", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/24.jpg" },
                tags: ["adult", "stepsister", "virginity", "family"],
                category: "new_videos",
                subcategory: "stepsister",
                views: 48700,
                likes: 1320,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_25",
                title: "Miho Ichiki Shows off Big Tits before taking Hardcore Creampie",
                description: "Miho Ichiki Shows off Big Tits before taking Hardcore Creampie with Passion",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Miho Ichiki Shows off Big Tits before taking Hardcore Creampie with Passion - Pornhub.com.ts",
                duration: "28:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_25", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/25.jpg" },
                tags: ["adult", "japanese", "big tits", "creampie", "hardcore"],
                category: "new_videos",
                subcategory: "asian",
                views: 41800,
                likes: 1150,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_26",
                title: "My 18-Year-Old Step-Sister wants to Feel my Cock inside Her",
                description: "My 18-Year-Old Step-Sister wants to Feel my Cock inside Her💦",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/My 18-Year-Old Step-Sister wants to Feel my Cock inside Her💦 - Pornhub.com.ts",
                duration: "24:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_26", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/26.jpg" },
                tags: ["adult", "stepsister", "18+", "family"],
                category: "new_videos",
                subcategory: "stepsister",
                views: 39600,
                likes: 1050,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_27",
                title: "My Pakistani Nurse want Fucked in Bedroom",
                description: "My Pakistani Nurse want Fucked in Bedroom-Clear Hindi Audio",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/My Pakistani Nurse want Fucked in Bedroom-Clear Hindi Audio 💦 - Pornhub.com.ts",
                duration: "21:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_27", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/27.jpg" },
                tags: ["adult", "pakistani", "nurse", "hindi"],
                category: "new_videos",
                subcategory: "general",
                views: 33400,
                likes: 920,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_28",
                title: "PUSSY TO ASS TO SQUIRT TO CUM!! SPECIAL",
                description: "PUSSY TO ASS TO SQUIRT TO CUM!! SPECIAL",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/PUSSY TO ASS TO SQUIRT TO CUM!! SPECIAL - Pornhub.com.ts",
                duration: "26:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_28", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/28.jpg" },
                tags: ["adult", "anal", "squirt"],
                category: "new_videos",
                subcategory: "general",
                views: 41800,
                likes: 1150,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_29",
                title: "PropertySex Desperate Agent looking to get a Quick Deal",
                description: "PropertySex Desperate Agent looking to get a Quick Deal from the Horny Home Seller",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/PropertySex Desperate Agent looking to get a Quick Deal from the Horny Home Seller - Pornhub.com.ts",
                duration: "28:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_29", username: "PropertySex", avatar: "https://randomuser.me/api/portraits/lego/29.jpg" },
                tags: ["adult", "real estate", "agent"],
                category: "new_videos",
                subcategory: "general",
                views: 37200,
                likes: 1020,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_30",
                title: "Public Agent Happy Pakistani Woman Fucks a Stranger",
                description: "Public Agent Happy Pakistani Woman Fucks a Stranger in the Public Toilets",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Public Agent Happy Pakistani Woman Fucks a Stranger in the Public Toilets - Pornhub.com.ts",
                duration: "23:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_30", username: "PublicAgent", avatar: "https://randomuser.me/api/portraits/lego/30.jpg" },
                tags: ["adult", "public", "pakistani", "agent"],
                category: "new_videos",
                subcategory: "general",
                views: 29800,
                likes: 810,
                rating: 4.3,
                formats: { ts: true }
            },
            {
                id: "v_31",
                title: "Sex with my Boss",
                description: "Sex with my Boss🔥🔥من برای پول با رئیسم رابطه جنسی دارم",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Sex with my Boss🔥🔥من برای پول با رئیسم رابطه جنسی دارم - Pornhub.com.ts",
                duration: "19:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_31", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/31.jpg" },
                tags: ["adult", "boss", "persian"],
                category: "new_videos",
                subcategory: "general",
                views: 35600,
                likes: 980,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_32",
                title: "Sharing a Bed with my best Friends HOT Girlfriend",
                description: "Sharing a Bed with my best Friends HOT Girlfriend",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Sharing a Bed with my best Friends HOT Girlfriend - Pornhub.com.ts",
                duration: "25:10",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_32", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/32.jpg" },
                tags: ["adult", "girlfriend", "sharing"],
                category: "new_videos",
                subcategory: "general",
                views: 42300,
                likes: 1180,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_33",
                title: "Sharing the Bed with Big Titty Step Sis",
                description: "Sharing the Bed with Big Titty Step Sis",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Sharing the Bed with Big Titty Step Sis - Pornhub.com.ts",
                duration: "22:35",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_33", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/33.jpg" },
                tags: ["adult", "stepsister", "big tits"],
                category: "new_videos",
                subcategory: "family",
                views: 38900,
                likes: 1070,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_34",
                title: "Step Sister's Anal Obsession",
                description: "Step Sister's Anal Obsession",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Step Sister's Anal Obsession. - Pornhub.com.ts",
                duration: "27:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_34", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/34.jpg" },
                tags: ["adult", "stepsister", "anal"],
                category: "new_videos",
                subcategory: "family",
                views: 44200,
                likes: 1220,
                rating: 4.8,
                formats: { ts: true }
            },
            {
                id: "v_35",
                title: "Stepsis Told Me to Take the Condom Off",
                description: "Stepsis Told Me to Take the Condom Off",
                thumbnail: "/categories/Thumbnails/Stepsis Told Me to Take the Condom Off.jpg",
                videoUrl: "/categories/Videos/Stepsis Told Me to Take the Condom Off.mp4",
                duration: "20:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_35", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/35.jpg" },
                tags: ["adult", "stepsister", "condom"],
                category: "new_videos",
                subcategory: "family",
                views: 36700,
                likes: 1010,
                rating: 4.5,
                formats: { mp4: true }
            },
            {
                id: "v_36",
                title: "Studying with Thick Asian Step Sister - Suki Sin",
                description: "Studying with Thick Asian Step Sister - Suki Sin - Family Therapy - Alex Adams",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Studying with Thick Asian Step Sister - Suki Sin - Family Therapy - Alex Adams - Pornhub.com.ts",
                duration: "31:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_36", username: "AlexAdams", avatar: "https://randomuser.me/api/portraits/lego/36.jpg" },
                tags: ["adult", "asian", "stepsister", "family therapy"],
                category: "new_videos",
                subcategory: "family",
                views: 47800,
                likes: 1320,
                rating: 4.9,
                formats: { ts: true }
            },
            {
                id: "v_37",
                title: "Sensual Massage",
                description: "Submissive Receives more than just an Oily Massage",
                thumbnail: "categories/Thumbnails/Submissive Receives more than just an Oily Massage. - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/Submissive Receives more than just an Oily Massage. - Pornhub.com.ts",
                duration: "24:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_37", username: "MessageExpert", avatar: "https://randomuser.me/api/portraits/lego/37.jpg" },
                tags: ["adult", "massage", "submissive", "oily"],
                category: "new_videos",
                subcategory: "general",
                views: 32100,
                likes: 880,
                rating: 4.3,
                formats: { ts: true }
            },
            {
                id: "v_38",
                title: "TRY TO GET ORGASM AND CREAM CUM AGIN ON WEBCAM",
                description: "TRY TO GET ORGASM AND CREAM CUM AGIN ON WEBCAM",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/TRY TO GET ORGASM AND CREAM CUM AGIN ON WEBCAM - Pornhub.com.ts",
                duration: "18:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_38", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/38.jpg" },
                tags: ["adult", "webcam", "orgasm"],
                category: "new_videos",
                subcategory: "general",
                views: 28400,
                likes: 780,
                rating: 4.2,
                formats: { ts: true }
            },
            {
                id: "v_39",
                title: "VIXENPLUS Japanese Journalist vs the BIGGEST BBC IN THE WORLD",
                description: "VIXENPLUS Japanese Journalist vs the BIGGEST BBC IN THE WORLD",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/VIXENPLUS Japanese Journalist vs the BIGGEST BBC IN THE WORLD - Pornhub.com.ts",
                duration: "29:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_39", username: "VixenPlus", avatar: "https://randomuser.me/api/portraits/lego/39.jpg" },
                tags: ["adult", "japanese", "bbc", "journalist"],
                category: "new_videos",
                subcategory: "general",
                views: 51200,
                likes: 1410,
                rating: 4.8,
                formats: { ts: true }
            },
            {
                id: "v_40",
                title: "WOWGIRLS Jia Lissa and Lena Reif have Incredibly Hot Sex",
                description: "WOWGIRLS Jia Lissa and Lena Reif have Incredibly Hot Sex on their first Lesbian Date",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/WOWGIRLS Jia Lissa and Lena Reif have Incredibly Hot Sex on their first Lesbian Date. - Pornhub.com.ts",
                duration: "26:40",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_40", username: "WowGirls", avatar: "https://randomuser.me/api/portraits/lego/40.jpg" },
                tags: ["adult", "lesbian", "jia lissa", "lena reif"],
                category: "new_videos",
                subcategory: "lesbian",
                views: 43600,
                likes: 1200,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_41",
                title: "Waking Up Next to Perfect Booty Step Sister - Zoey Di Giacomo",
                description: "Waking Up Next to Perfect Booty Step Sister - Zoey Di Giacomo - Family Therapy - Alex Adams",
                thumbnail: "/categories/Thumbnails/Waking Up Next to Perfect Booty Step Sister - Zoey Di Giacomo - Family Therapy - Alex Adams.jpg",
                videoUrl: "/categories/Videos/Waking Up Next to Perfect Booty Step Sister - Zoey Di Giacomo - Family Therapy - Alex Adams.mp4",
                duration: "33:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_41", username: "AlexAdams", avatar: "https://randomuser.me/api/portraits/lego/41.jpg" },
                tags: ["adult", "stepsister", "family therapy", "zoey di giacomo"],
                category: "new_videos",
                subcategory: "family",
                views: 49800,
                likes: 1370,
                rating: 4.9,
                formats: { mp4: true }
            },
            {
                id: "v_42",
                title: "Watch Two lesbians kissing with massive tits",
                description: "Watch Two lesbians kissing with massive tits - Xnxx, Kissing, Lesbian Porn",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Watch Two lesbians kissing with massive tits - Xnxx, Kissing, Lesbian Porn.mp4",
                duration: "15:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_42", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/42.jpg" },
                tags: ["adult", "lesbian", "kissing", "massive tits"],
                category: "new_videos",
                subcategory: "lesbian",
                views: 34200,
                likes: 940,
                rating: 4.4,
                formats: { mp4: true }
            },
            {
                id: "v_43",
                title: "We got too Slippery Playing with Lotion… Ended up Creampied",
                description: "We got too Slippery Playing with Lotion… Ended up Creampied",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/We got too Slippery Playing with Lotion… Ended up Creampied. - Pornhub.com.ts",
                duration: "21:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_43", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/43.jpg" },
                tags: ["adult", "lotion", "creampie", "slippery"],
                category: "new_videos",
                subcategory: "general",
                views: 37800,
                likes: 1040,
                rating: 4.5,
                formats: { ts: true }
            },
            {
                id: "v_44",
                title: "Wow! I didn't think my Stepsister's Huge Ass would Turn me On",
                description: "Wow! I didn't think my Stepsister's Huge Ass would Turn me On",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Wow! I didn't think my Stepsister's Huge Ass would Turn me On. - Pornhub.com.ts",
                duration: "23:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_44", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/44.jpg" },
                tags: ["adult", "stepsister", "huge ass"],
                category: "new_videos",
                subcategory: "family",
                views: 41300,
                likes: 1140,
                rating: 4.6,
                formats: { ts: true }
            },
            {
                id: "v_45",
                title: "Step Sister Lends me her Ass, Part 4",
                description: "is it Opening Up. - Step Sister Lends me her Ass, Part 4",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/is it Opening Up. - Step Sister Lends me her Ass, Part 4 - Pornhub.com.ts",
                duration: "25:50",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_45", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/45.jpg" },
                tags: ["adult", "stepsister", "anal", "part 4"],
                category: "new_videos",
                subcategory: "family",
                views: 39700,
                likes: 1090,
                rating: 4.7,
                formats: { ts: true }
            },
            {
                id: "v_46",
                title: "Первый анал для девушки с большими сиськами",
                description: "Первый анал для девушки с большими сиськами - Оцени мою новую девушку в видео от первого лица",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/Первый анал для девушки с большими сиськами - Оцени мою новую девушку в видео от первого лица - Pornhub.com.ts",
                duration: "22:30",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_46", username: "RussianModel", avatar: "https://randomuser.me/api/portraits/lego/46.jpg" },
                tags: ["adult", "russian", "anal", "big tits"],
                category: "new_videos",
                subcategory: "general",
                views: 35400,
                likes: 970,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_47",
                title: "Passionate Romance",
                description: "【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6",
                thumbnail: "categories/Thumbnails/【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6 - Pornhub.com.jpg",
                videoUrl: "/categories/Videos/【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6 - Pornhub.com.mp4",
                duration: "28:15",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_47", username: "RomanticLover", avatar: "https://randomuser.me/api/portraits/lego/47.jpg" },
                tags: ["adult", "romantic", "passionate", "love"],
                category: "new_videos",
                subcategory: "romantic",
                views: 31800,
                likes: 870,
                rating: 4.3,
                formats: { mp4: true }
            },
            {
                id: "v_48",
                title: "パイズリ手コキ集",
                description: "パイズリ手コキ集",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/パイズリ手コキ集 - Pornhub.com.ts",
                duration: "19:45",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_48", username: "JapaneseModel", avatar: "https://randomuser.me/api/portraits/lego/48.jpg" },
                tags: ["adult", "japanese", "compilation"],
                category: "new_videos",
                subcategory: "general",
                views: 27600,
                likes: 760,
                rating: 4.2,
                formats: { ts: true }
            },
            {
                id: "v_49",
                title: "元グラビア出身のiカップ爆乳の医療学",
                description: "元グラビア出身のiカップ爆乳の医療学.せ.いあいりちゃん。",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/元グラビア出身のiカップ爆乳の医療学.せ.いあいりちゃん。 - Pornhub.com.ts",
                duration: "24:20",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_49", username: "GravureModel", avatar: "https://randomuser.me/api/portraits/lego/49.jpg" },
                tags: ["adult", "japanese", "gravure", "big tits"],
                category: "new_videos",
                subcategory: "general",
                views: 33200,
                likes: 910,
                rating: 4.4,
                formats: { ts: true }
            },
            {
                id: "v_50",
                title: "長い間セックスをしてなかったから何度も中出しと顔射で精液まみれになって妊娠しちゃうかもしれない",
                description: "長い間セックスをしてなかったから何度も中出しと顔射で精液まみれになって妊娠しちゃうかもしれない❤️",
                thumbnail: "/file.svg",
                videoUrl: "/categories/Videos/長い間セックスをしてなかったから何度も中出しと顔射で精液まみれになって妊娠しちゃうかもしれない❤️ - Pornhub.com.ts",
                duration: "26:35",
                uploadDate: "2025-07-02",
                uploader: { id: "u_new_50", username: "JapaneseModel", avatar: "https://randomuser.me/api/portraits/lego/50.jpg" },
                tags: ["adult", "japanese", "creampie"],
                category: "new_videos",
                subcategory: "general",
                views: 38900,
                likes: 1070,
                rating: 4.5,
                formats: { ts: true }
            }
        ];
    }

    getEmbeddedPhotoData() {
        // Photo data from the categories - embedded directly
        return [
            {
                id: "p_1",
                title: "Has Sized",
                description: "Has Sized - High quality photo",
                thumbnail: "categories/Photos/006_has-sized.webp",
                videoUrl: "categories/Photos/006_has-sized.webp", // For photos, this is the image URL
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_1", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/10.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1250,
                likes: 85,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_2",
                title: "Won Her Pussy",
                description: "Won Her Pussy - High quality photo",
                thumbnail: "/categories/Photos/009_won-her-pussy.webp",
                videoUrl: "/categories/Photos/009_won-her-pussy.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_2", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/11.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 980,
                likes: 67,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_3",
                title: "Skinny During Sex",
                description: "Skinny During Sex - High quality photo",
                thumbnail: "/categories/Photos/011_skinny-during-sex.webp",
                videoUrl: "/categories/Photos/011_skinny-during-sex.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_3", username: "GalleryAdmin", avatar: "https://randomuser.me/api/portraits/lego/12.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 95,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_4",
                title: "Girl To",
                description: "Girl To - High quality photo",
                thumbnail: "/categories/Photos/012_girl-to.webp",
                videoUrl: "/categories/Photos/012_girl-to.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_4", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/13.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 890,
                likes: 62,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_5",
                title: "Flag",
                description: "Flag - High quality photo",
                thumbnail: "/categories/Photos/019_flag-.webp",
                videoUrl: "/categories/Photos/019_flag-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_5", username: "PhotoArtist", avatar: "https://randomuser.me/api/portraits/lego/14.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1120,
                likes: 78,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_6",
                title: "Breasty",
                description: "Breasty - High quality photo",
                thumbnail: "/categories/Photos/023_breasty.webp",
                videoUrl: "/categories/Photos/023_breasty.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_6", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/15.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1350,
                likes: 89,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_7",
                title: "Works Lot Gets",
                description: "Works Lot Gets - High quality photo",
                thumbnail: "/categories/Photos/025_works-lot-gets.webp",
                videoUrl: "/categories/Photos/025_works-lot-gets.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_7", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/16.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 980,
                likes: 65,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_8",
                title: "Athletic",
                description: "Athletic - High quality photo",
                thumbnail: "/categories/Photos/035_athletic.webp",
                videoUrl: "/categories/Photos/035_athletic.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_8", username: "FitnessModel", avatar: "https://randomuser.me/api/portraits/lego/17.jpg" },
                tags: ["photo", "gallery", "athletic", "fitness"],
                category: "photos",
                subcategory: "gallery",
                views: 1580,
                likes: 102,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_9",
                title: "Hispanic On",
                description: "Hispanic On - High quality photo",
                thumbnail: "/categories/Photos/035_hispanic-on-.webp",
                videoUrl: "/categories/Photos/035_hispanic-on-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_9", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/18.jpg" },
                tags: ["photo", "gallery", "hispanic"],
                category: "photos",
                subcategory: "gallery",
                views: 1240,
                likes: 81,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_10",
                title: "Sexy Be Visitor",
                description: "Sexy Be Visitor - High quality photo",
                thumbnail: "/categories/Photos/041_sexy-be-visitor.webp",
                videoUrl: "/categories/Photos/041_sexy-be-visitor.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_10", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/19.jpg" },
                tags: ["photo", "gallery", "sexy"],
                category: "photos",
                subcategory: "gallery",
                views: 1420,
                likes: 93,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_11",
                title: "A Makes",
                description: "A Makes - High quality photo",
                thumbnail: "/categories/Photos/042_a-makes-.webp",
                videoUrl: "/categories/Photos/042_a-makes-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_11", username: "PhotoArtist", avatar: "https://randomuser.me/api/portraits/lego/20.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 890,
                likes: 58,
                rating: 3.9,
                type: "photo"
            },
            {
                id: "p_12",
                title: "Cougar",
                description: "Cougar - High quality photo",
                thumbnail: "/categories/Photos/061_cougar.webp",
                videoUrl: "/categories/Photos/061_cougar.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_12", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/21.jpg" },
                tags: ["photo", "gallery", "cougar"],
                category: "photos",
                subcategory: "gallery",
                views: 1680,
                likes: 115,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_13",
                title: "From",
                description: "From - High quality photo",
                thumbnail: "/categories/Photos/103_from.webp",
                videoUrl: "/categories/Photos/103_from.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_13", username: "ContentCreator", avatar: "https://randomuser.me/api/portraits/lego/22.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1150,
                likes: 76,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_14",
                title: "Huge Desires",
                description: "Huge Desires - High quality photo",
                thumbnail: "/categories/Photos/107_huge-desires-.webp",
                videoUrl: "/categories/Photos/107_huge-desires-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_14", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/23.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1380,
                likes: 91,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_15",
                title: "Latina Hairless",
                description: "Latina Hairless - High quality photo",
                thumbnail: "/categories/Photos/116_latina-hairless.webp",
                videoUrl: "/categories/Photos/116_latina-hairless.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_15", username: "LatinaModel", avatar: "https://randomuser.me/api/portraits/lego/24.jpg" },
                tags: ["photo", "gallery", "latina"],
                category: "photos",
                subcategory: "gallery",
                views: 1520,
                likes: 98,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_16",
                title: "Blondie Is",
                description: "Blondie Is - High quality photo",
                thumbnail: "/categories/Photos/117_blondie-is.webp",
                videoUrl: "/categories/Photos/117_blondie-is.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_16", username: "BlondieModel", avatar: "https://randomuser.me/api/portraits/lego/25.jpg" },
                tags: ["photo", "gallery", "blonde"],
                category: "photos",
                subcategory: "gallery",
                views: 1290,
                likes: 84,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_17",
                title: "Natural",
                description: "Natural - High quality photo",
                thumbnail: "/categories/Photos/118_natural.webp",
                videoUrl: "/categories/Photos/118_natural.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_17", username: "NaturalBeauty", avatar: "https://randomuser.me/api/portraits/lego/26.jpg" },
                tags: ["photo", "gallery", "natural"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 95,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_18",
                title: "Terrific Has",
                description: "Terrific Has - High quality photo",
                thumbnail: "/categories/Photos/120-terrific-has.webp",
                videoUrl: "/categories/Photos/120-terrific-has.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_18", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/27.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1180,
                likes: 77,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_19",
                title: "13944",
                description: "13944 - High quality photo",
                thumbnail: "/categories/Photos/13944.jpg",
                videoUrl: "/categories/Photos/13944.jpg",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_19", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/28.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 920,
                likes: 61,
                rating: 3.9,
                type: "photo"
            },
            {
                id: "p_20",
                title: "151576",
                description: "151576 - High quality photo",
                thumbnail: "/categories/Photos/151576.jpg",
                videoUrl: "/categories/Photos/151576.jpg",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_20", username: "PhotoCollector", avatar: "https://randomuser.me/api/portraits/lego/29.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1050,
                likes: 69,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_21",
                title: "This And",
                description: "This And - High quality photo",
                thumbnail: "/categories/Photos/162_this-and.webp",
                videoUrl: "/categories/Photos/162_this-and.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_21", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/31.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1320,
                likes: 87,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_22",
                title: "Second Girlfriend (1)",
                description: "Second Girlfriend (1) - High quality photo",
                thumbnail: "/categories/Photos/172_second-girlfriend (1).webp",
                videoUrl: "/categories/Photos/172_second-girlfriend (1).webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_22", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/32.jpg" },
                tags: ["photo", "girlfriend", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1580,
                likes: 104,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_23",
                title: "Second Girlfriend",
                description: "Second Girlfriend - High quality photo",
                thumbnail: "/categories/Photos/172_second-girlfriend.webp",
                videoUrl: "/categories/Photos/172_second-girlfriend.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_23", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/33.jpg" },
                tags: ["photo", "girlfriend", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1420,
                likes: 93,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_24",
                title: "With Round",
                description: "With Round - High quality photo",
                thumbnail: "/categories/Photos/172_with-round.webp",
                videoUrl: "/categories/Photos/172_with-round.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_24", username: "CurveModel", avatar: "https://randomuser.me/api/portraits/lego/34.jpg" },
                tags: ["photo", "round", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1680,
                likes: 110,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_25",
                title: "Surprising",
                description: "Surprising - High quality photo",
                thumbnail: "/categories/Photos/173_surprising.webp",
                videoUrl: "/categories/Photos/173_surprising.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_25", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/35.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1150,
                likes: 76,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_26",
                title: "Relishing The",
                description: "Relishing The - High quality photo",
                thumbnail: "/categories/Photos/222_relishing-the.webp",
                videoUrl: "/categories/Photos/222_relishing-the.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_26", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/36.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1277,
                likes: 122,
                rating: 4.9,
                type: "photo"
            },
            {
                id: "p_27",
                title: "Anna Fuck",
                description: "Anna Fuck - High quality photo",
                thumbnail: "/categories/Photos/242_anna-fuck-.webp",
                videoUrl: "/categories/Photos/242_anna-fuck-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_27", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/37.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 3769,
                likes: 302,
                rating: 3.5,
                type: "photo"
            },
            {
                id: "p_28",
                title: "In Of",
                description: "In Of - High quality photo",
                thumbnail: "/categories/Photos/243_in-of.webp",
                videoUrl: "/categories/Photos/243_in-of.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_28", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/38.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 941,
                likes: 75,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_29",
                title: "260361",
                description: "260361 - High quality photo",
                thumbnail: "/categories/Photos/260361.jpg",
                videoUrl: "/categories/Photos/260361.jpg",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_29", username: "PhotoCollector", avatar: "https://randomuser.me/api/portraits/lego/39.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1456,
                likes: 116,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_30",
                title: "Lad Move",
                description: "Lad Move - High quality photo",
                thumbnail: "/categories/Photos/292_lad-move.webp",
                videoUrl: "/categories/Photos/292_lad-move.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_30", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/40.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2134,
                likes: 170,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_31",
                title: "Brunette Double In",
                description: "Brunette Double In - High quality photo",
                thumbnail: "/categories/Photos/298_brunette-double-in.webp",
                videoUrl: "/categories/Photos/298_brunette-double-in.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_31", username: "BrunetteModel", avatar: "https://randomuser.me/api/portraits/lego/41.jpg" },
                tags: ["photo", "brunette", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1890,
                likes: 145,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_32",
                title: "Ann Titties After",
                description: "Ann Titties After - High quality photo",
                thumbnail: "/categories/Photos/318_ann-titties-after.webp",
                videoUrl: "/categories/Photos/318_ann-titties-after.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_32", username: "AnnModel", avatar: "https://randomuser.me/api/portraits/lego/42.jpg" },
                tags: ["photo", "ann", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2340,
                likes: 187,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_33",
                title: "Love",
                description: "Love - High quality photo",
                thumbnail: "/categories/Photos/320_love.webp",
                videoUrl: "/categories/Photos/320_love.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_33", username: "LoveModel", avatar: "https://randomuser.me/api/portraits/lego/43.jpg" },
                tags: ["photo", "love", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1650,
                likes: 132,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_34",
                title: "With Her",
                description: "With Her - High quality photo",
                thumbnail: "/categories/Photos/323_with-her-.webp",
                videoUrl: "/categories/Photos/323_with-her-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_34", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/44.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1420,
                likes: 113,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_35",
                title: "Threesome",
                description: "Threesome - High quality photo",
                thumbnail: "/categories/Photos/348_threesome.webp",
                videoUrl: "/categories/Photos/348_threesome.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_35", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/45.jpg" },
                tags: ["photo", "threesome", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2780,
                likes: 223,
                rating: 4.7,
                type: "photo"
            },
            {
                id: "p_36",
                title: "Off Gets Handsome",
                description: "Off Gets Handsome - High quality photo",
                thumbnail: "/categories/Photos/354-off-gets-handsome.webp",
                videoUrl: "/categories/Photos/354-off-gets-handsome.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_36", username: "HandsomeModel", avatar: "https://randomuser.me/api/portraits/lego/46.jpg" },
                tags: ["photo", "handsome", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1560,
                likes: 124,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_37",
                title: "With Big Gorgeous",
                description: "With Big Gorgeous - High quality photo",
                thumbnail: "/categories/Photos/357_with-big-gorgeous.webp",
                videoUrl: "/categories/Photos/357_with-big-gorgeous.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_37", username: "GorgeousModel", avatar: "https://randomuser.me/api/portraits/lego/47.jpg" },
                tags: ["photo", "gorgeous", "big", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2450,
                likes: 196,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_38",
                title: "Girl Naked Session",
                description: "Girl Naked Session - High quality photo",
                thumbnail: "/categories/Photos/372_girl-naked-session.webp",
                videoUrl: "/categories/Photos/372_girl-naked-session.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_38", username: "SessionModel", avatar: "https://randomuser.me/api/portraits/lego/48.jpg" },
                tags: ["photo", "naked", "session", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 3120,
                likes: 250,
                rating: 4.8,
                type: "photo"
            },
            {
                id: "p_39",
                title: "Supremacy",
                description: "Supremacy - High quality photo",
                thumbnail: "/categories/Photos/390_supremacy.webp",
                videoUrl: "/categories/Photos/390_supremacy.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_39", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/49.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1340,
                likes: 107,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_40",
                title: "Guy Holes",
                description: "Guy Holes - High quality photo",
                thumbnail: "/categories/Photos/399_guy_holes.webp",
                videoUrl: "/categories/Photos/399_guy_holes.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_40", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/50.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1780,
                likes: 142,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_41",
                title: "Exposes",
                description: "Exposes - High quality photo",
                thumbnail: "/categories/Photos/400_exposes.webp",
                videoUrl: "/categories/Photos/400_exposes.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_41", username: "ExposeModel", avatar: "https://randomuser.me/api/portraits/lego/51.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1920,
                likes: 153,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_42",
                title: "Call Truly Horny",
                description: "Call Truly Horny - High quality photo",
                thumbnail: "/categories/Photos/408_call-truly-horny.webp",
                videoUrl: "/categories/Photos/408_call-truly-horny.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_42", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/52.jpg" },
                tags: ["photo", "horny", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2650,
                likes: 212,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_43",
                title: "Secretary",
                description: "Secretary - High quality photo",
                thumbnail: "/categories/Photos/416_secretary.webp",
                videoUrl: "/categories/Photos/416_secretary.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_43", username: "SecretaryModel", avatar: "https://randomuser.me/api/portraits/lego/53.jpg" },
                tags: ["photo", "secretary", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2180,
                likes: 174,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_44",
                title: "Lovely By",
                description: "Lovely By - High quality photo",
                thumbnail: "/categories/Photos/417_lovely-by.webp",
                videoUrl: "/categories/Photos/417_lovely-by.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_44", username: "LovelyModel", avatar: "https://randomuser.me/api/portraits/lego/54.jpg" },
                tags: ["photo", "lovely", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1590,
                likes: 127,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_45",
                title: "Heels Assets",
                description: "Heels Assets - High quality photo",
                thumbnail: "/categories/Photos/446_heels-assets.webp",
                videoUrl: "/categories/Photos/446_heels-assets.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_45", username: "HeelsModel", avatar: "https://randomuser.me/api/portraits/lego/55.jpg" },
                tags: ["photo", "heels", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2340,
                likes: 187,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_46",
                title: "Her In",
                description: "Her In - High quality photo",
                thumbnail: "/categories/Photos/461_her-in.webp",
                videoUrl: "/categories/Photos/461_her-in.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_46", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/56.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 116,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_47",
                title: "Anal",
                description: "Anal - High quality photo",
                thumbnail: "/categories/Photos/464-anal.webp",
                videoUrl: "/categories/Photos/464-anal.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_47", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/57.jpg" },
                tags: ["photo", "anal", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2890,
                likes: 231,
                rating: 4.7,
                type: "photo"
            },
            {
                id: "p_48",
                title: "464",
                description: "464 - High quality photo",
                thumbnail: "/categories/Photos/464.webp",
                videoUrl: "/categories/Photos/464.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_48", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/58.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1230,
                likes: 98,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_49",
                title: "Slaps With",
                description: "Slaps With - High quality photo",
                thumbnail: "/categories/Photos/483_slaps-with.webp",
                videoUrl: "/categories/Photos/483_slaps-with.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_49", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/59.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1680,
                likes: 134,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_50",
                title: "For And Passion",
                description: "For And Passion - High quality photo",
                thumbnail: "/categories/Photos/523-for-and-passion.webp",
                videoUrl: "/categories/Photos/523-for-and-passion.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_50", username: "PassionModel", avatar: "https://randomuser.me/api/portraits/lego/60.jpg" },
                tags: ["photo", "passion", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2120,
                likes: 169,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_51",
                title: "524",
                description: "524 - High quality photo",
                thumbnail: "/categories/Photos/524.webp",
                videoUrl: "/categories/Photos/524.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_51", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/61.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1340,
                likes: 107,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_52",
                title: "Porky Additionally",
                description: "Porky Additionally - High quality photo",
                thumbnail: "/categories/Photos/525_porky-additionally.webp",
                videoUrl: "/categories/Photos/525_porky-additionally.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_52", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/62.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1560,
                likes: 124,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_53",
                title: "Authorizing A Friend",
                description: "Authorizing A Friend - High quality photo",
                thumbnail: "/categories/Photos/540_authorizing-a-friend.webp",
                videoUrl: "/categories/Photos/540_authorizing-a-friend.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_53", username: "FriendModel", avatar: "https://randomuser.me/api/portraits/lego/63.jpg" },
                tags: ["photo", "friend", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1780,
                likes: 142,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_54",
                title: "Petite Body With",
                description: "Petite Body With - High quality photo",
                thumbnail: "/categories/Photos/588_petite-body-with.webp",
                videoUrl: "/categories/Photos/588_petite-body-with.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_54", username: "PetiteModel", avatar: "https://randomuser.me/api/portraits/lego/64.jpg" },
                tags: ["photo", "petite", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2450,
                likes: 196,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_55",
                title: "Ria Besides",
                description: "Ria Besides - High quality photo",
                thumbnail: "/categories/Photos/601_ria-besides.webp",
                videoUrl: "/categories/Photos/601_ria-besides.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_55", username: "RiaModel", avatar: "https://randomuser.me/api/portraits/lego/65.jpg" },
                tags: ["photo", "ria", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1890,
                likes: 151,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_56",
                title: "Porn Backdoor (1)",
                description: "Porn Backdoor (1) - High quality photo",
                thumbnail: "/categories/Photos/608_porn_backdoor (1).webp",
                videoUrl: "/categories/Photos/608_porn_backdoor (1).webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_56", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/66.jpg" },
                tags: ["photo", "backdoor", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2680,
                likes: 214,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_57",
                title: "Porn Backdoor",
                description: "Porn Backdoor - High quality photo",
                thumbnail: "/categories/Photos/608_porn_backdoor.webp",
                videoUrl: "/categories/Photos/608_porn_backdoor.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_57", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/67.jpg" },
                tags: ["photo", "backdoor", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2590,
                likes: 207,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_58",
                title: "Needs",
                description: "Needs - High quality photo",
                thumbnail: "/categories/Photos/616_-needs-.webp",
                videoUrl: "/categories/Photos/616_-needs-.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_58", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/68.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1420,
                likes: 113,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_59",
                title: "Slim With Pussy",
                description: "Slim With Pussy - High quality photo",
                thumbnail: "/categories/Photos/626_slim-with-pussy.webp",
                videoUrl: "/categories/Photos/626_slim-with-pussy.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_59", username: "SlimModel", avatar: "https://randomuser.me/api/portraits/lego/69.jpg" },
                tags: ["photo", "slim", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 3120,
                likes: 249,
                rating: 4.7,
                type: "photo"
            },
            {
                id: "p_60",
                title: "Each About",
                description: "Each About - High quality photo",
                thumbnail: "/categories/Photos/639_each-about.webp",
                videoUrl: "/categories/Photos/639_each-about.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_60", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/70.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1340,
                likes: 107,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_61",
                title: "Exudes Air Of",
                description: "Exudes Air Of - High quality photo",
                thumbnail: "/categories/Photos/646_exudes-air-of.webp",
                videoUrl: "/categories/Photos/646_exudes-air-of.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_61", username: "ElegantModel", avatar: "https://randomuser.me/api/portraits/lego/71.jpg" },
                tags: ["photo", "elegant", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1780,
                likes: 142,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_62",
                title: "Ponytail",
                description: "Ponytail - High quality photo",
                thumbnail: "/categories/Photos/659_ponytail.webp",
                videoUrl: "/categories/Photos/659_ponytail.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_62", username: "PonytailModel", avatar: "https://randomuser.me/api/portraits/lego/72.jpg" },
                tags: ["photo", "ponytail", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2230,
                likes: 178,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_63",
                title: "Rika",
                description: "Rika - High quality photo",
                thumbnail: "/categories/Photos/682_rika.webp",
                videoUrl: "/categories/Photos/682_rika.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_63", username: "RikaModel", avatar: "https://randomuser.me/api/portraits/lego/73.jpg" },
                tags: ["photo", "rika", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1950,
                likes: 156,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_64",
                title: "Covered After",
                description: "Covered After - High quality photo",
                thumbnail: "/categories/Photos/684_-covered-after.webp",
                videoUrl: "/categories/Photos/684_-covered-after.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_64", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/74.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1560,
                likes: 124,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_65",
                title: "On Cock Sucking",
                description: "On Cock Sucking - High quality photo",
                thumbnail: "/categories/Photos/723_on-cock-sucking.webp",
                videoUrl: "/categories/Photos/723_on-cock-sucking.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_65", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/75.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2890,
                likes: 231,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_66",
                title: "And Besides Gets",
                description: "And Besides Gets - High quality photo",
                thumbnail: "/categories/Photos/737_and-besides-gets.webp",
                videoUrl: "/categories/Photos/737_and-besides-gets.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_66", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/76.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 116,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_67",
                title: "Your Does",
                description: "Your Does - High quality photo",
                thumbnail: "/categories/Photos/737_your-does.webp",
                videoUrl: "/categories/Photos/737_your-does.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_67", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/77.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1320,
                likes: 105,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_68",
                title: "Giving Dong",
                description: "Giving Dong - High quality photo",
                thumbnail: "/categories/Photos/769_giving-dong.webp",
                videoUrl: "/categories/Photos/769_giving-dong.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_68", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/78.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2340,
                likes: 187,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_69",
                title: "80483",
                description: "80483 - High quality photo",
                thumbnail: "/categories/Photos/80483.jpg",
                videoUrl: "/categories/Photos/80483.jpg",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_69", username: "PhotoCollector", avatar: "https://randomuser.me/api/portraits/lego/79.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1680,
                likes: 134,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_70",
                title: "A",
                description: "A - High quality photo",
                thumbnail: "/categories/Photos/807_a.webp",
                videoUrl: "/categories/Photos/807_a.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_70", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/80.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1120,
                likes: 89,
                rating: 3.9,
                type: "photo"
            },
            {
                id: "p_71",
                title: "Worship A Dude",
                description: "Worship A Dude - High quality photo",
                thumbnail: "/categories/Photos/841_worship-a-dude.webp",
                videoUrl: "/categories/Photos/841_worship-a-dude.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_71", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/81.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1890,
                likes: 151,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_72",
                title: "Actual In Of",
                description: "Actual In Of - High quality photo",
                thumbnail: "/categories/Photos/849_actual-in-of.webp",
                videoUrl: "/categories/Photos/849_actual-in-of.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_72", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/82.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 116,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_73",
                title: "Her Oozy Addition",
                description: "Her Oozy Addition - High quality photo",
                thumbnail: "/categories/Photos/889_her-oozy-addition.webp",
                videoUrl: "/categories/Photos/889_her-oozy-addition.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_73", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/83.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2120,
                likes: 169,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_74",
                title: "Brunette",
                description: "Brunette - High quality photo",
                thumbnail: "/categories/Photos/890_brunette.webp",
                videoUrl: "/categories/Photos/890_brunette.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_74", username: "BrunetteModel", avatar: "https://randomuser.me/api/portraits/lego/84.jpg" },
                tags: ["photo", "brunette", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2560,
                likes: 204,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_75",
                title: "Pumped",
                description: "Pumped - High quality photo",
                thumbnail: "/categories/Photos/893_pumped.webp",
                videoUrl: "/categories/Photos/893_pumped.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_75", username: "FitnessModel", avatar: "https://randomuser.me/api/portraits/lego/85.jpg" },
                tags: ["photo", "pumped", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2340,
                likes: 187,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_76",
                title: "VRH0674 Luluchu Austin Pierce",
                description: "VRH0674 Luluchu Austin Pierce - High quality photo",
                thumbnail: "/categories/Photos/vrh0674_luluchu_austinpierce_180_078.jpg",
                videoUrl: "/categories/Photos/vrh0674_luluchu_austinpierce_180_078.jpg",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_76", username: "VRModel", avatar: "https://randomuser.me/api/portraits/lego/86.jpg" },
                tags: ["photo", "vr", "luluchu", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 3450,
                likes: 276,
                rating: 4.8,
                type: "photo"
            },
            {
                id: "p_77",
                title: "Those",
                description: "Those - High quality photo",
                thumbnail: "/categories/Photos/898_those.webp",
                videoUrl: "/categories/Photos/898_those.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_77", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/87.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1560,
                likes: 124,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_78",
                title: "Undressing Wow",
                description: "Undressing Wow - High quality photo",
                thumbnail: "/categories/Photos/920_undressing-wow.webp",
                videoUrl: "/categories/Photos/920_undressing-wow.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_78", username: "WowModel", avatar: "https://randomuser.me/api/portraits/lego/88.jpg" },
                tags: ["photo", "undressing", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2890,
                likes: 231,
                rating: 4.7,
                type: "photo"
            },
            {
                id: "p_79",
                title: "Satisfying",
                description: "Satisfying - High quality photo",
                thumbnail: "/categories/Photos/925_satisfying.webp",
                videoUrl: "/categories/Photos/925_satisfying.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_79", username: "SatisfyingModel", avatar: "https://randomuser.me/api/portraits/lego/89.jpg" },
                tags: ["photo", "satisfying", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2120,
                likes: 169,
                rating: 4.5,
                type: "photo"
            },
            {
                id: "p_80",
                title: "Deep",
                description: "Deep - High quality photo",
                thumbnail: "/categories/Photos/928_deep.webp",
                videoUrl: "/categories/Photos/928_deep.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_80", username: "DeepModel", avatar: "https://randomuser.me/api/portraits/lego/90.jpg" },
                tags: ["photo", "deep", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1780,
                likes: 142,
                rating: 4.3,
                type: "photo"
            },
            {
                id: "p_81",
                title: "And Being Somebody",
                description: "And Being Somebody - High quality photo",
                thumbnail: "/categories/Photos/931_and-being-somebody.webp",
                videoUrl: "/categories/Photos/931_and-being-somebody.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_81", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/91.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1450,
                likes: 116,
                rating: 4.1,
                type: "photo"
            },
            {
                id: "p_82",
                title: "Knows",
                description: "Knows - High quality photo",
                thumbnail: "/categories/Photos/935-knows.webp",
                videoUrl: "/categories/Photos/935-knows.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_82", username: "Guest", avatar: "https://randomuser.me/api/portraits/lego/92.jpg" },
                tags: ["photo", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1230,
                likes: 98,
                rating: 4.0,
                type: "photo"
            },
            {
                id: "p_83",
                title: "Shows",
                description: "Shows - High quality photo",
                thumbnail: "/categories/Photos/942_shows.webp",
                videoUrl: "/categories/Photos/942_shows.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_83", username: "ShowModel", avatar: "https://randomuser.me/api/portraits/lego/93.jpg" },
                tags: ["photo", "shows", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1680,
                likes: 134,
                rating: 4.2,
                type: "photo"
            },
            {
                id: "p_84",
                title: "Oiled Up For",
                description: "Oiled Up For - High quality photo",
                thumbnail: "/categories/Photos/954_oiled-up-for.webp",
                videoUrl: "/categories/Photos/954_oiled-up-for.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_84", username: "OiledModel", avatar: "https://randomuser.me/api/portraits/lego/94.jpg" },
                tags: ["photo", "oiled", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 2560,
                likes: 204,
                rating: 4.6,
                type: "photo"
            },
            {
                id: "p_85",
                title: "Exposes",
                description: "Exposes - High quality photo",
                thumbnail: "/categories/Photos/974_exposes.webp",
                videoUrl: "/categories/Photos/974_exposes.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_85", username: "ExposeModel", avatar: "https://randomuser.me/api/portraits/lego/95.jpg" },
                tags: ["photo", "exposes", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 1890,
                likes: 151,
                rating: 4.4,
                type: "photo"
            },
            {
                id: "p_86",
                title: "Hottest Black Quality",
                description: "Hottest Black Quality - High quality photo",
                thumbnail: "/categories/Photos/998_hottest-black-quality.webp",
                videoUrl: "/categories/Photos/998_hottest-black-quality.webp",
                duration: "Photo",
                uploadDate: "2025-07-02",
                uploader: { id: "u_photo_86", username: "HottestModel", avatar: "https://randomuser.me/api/portraits/lego/96.jpg" },
                tags: ["photo", "hottest", "black", "quality", "gallery"],
                category: "photos",
                subcategory: "gallery",
                views: 3120,
                likes: 249,
                rating: 4.8,
                type: "photo"
            }
        ];
    }

    getDefaultUsers() {
        return [
            {
                id: "u_new_1",
                username: "Guest",
                avatar: "https://randomuser.me/api/portraits/lego/1.jpg",
                verified: false,
                followers: 1250
            },
            {
                id: "u_new_2",
                username: "ContentCreator",
                avatar: "https://randomuser.me/api/portraits/lego/2.jpg",
                verified: true,
                followers: 5430
            },
            {
                id: "u_new_3",
                username: "BangBros",
                avatar: "https://randomuser.me/api/portraits/lego/3.jpg",
                verified: true,
                followers: 15200
            }
        ];
    }

    // Category filtering functionality
    filterByCategory(category) {
        if (category === 'all') {
            return [...this.videos, ...this.newPhotos];
        }

        const filteredVideos = this.videos.filter(video =>
            video.category === category || video.subcategory === category
        );

        const filteredPhotos = this.newPhotos.filter(photo =>
            photo.category === category || photo.subcategory === category
        );

        return [...filteredVideos, ...filteredPhotos];
    }

    filterBySubcategory(subcategory) {
        const filteredVideos = this.videos.filter(video =>
            video.subcategory === subcategory
        );

        const filteredPhotos = this.newPhotos.filter(photo =>
            photo.subcategory === subcategory
        );

        return [...filteredVideos, ...filteredPhotos];
    }

    filterByTags(tags) {
        if (!Array.isArray(tags)) {
            tags = [tags];
        }

        const filteredVideos = this.videos.filter(video =>
            tags.some(tag => video.tags.includes(tag))
        );

        const filteredPhotos = this.newPhotos.filter(photo =>
            tags.some(tag => photo.tags.includes(tag))
        );

        return [...filteredVideos, ...filteredPhotos];
    }

    filterByType(type) {
        if (type === 'video') {
            return this.videos;
        } else if (type === 'photo') {
            return this.newPhotos;
        }
        return [...this.videos, ...this.newPhotos];
    }

    // Advanced filtering with multiple criteria
    advancedFilter(criteria) {
        let results = [...this.videos, ...this.newPhotos];

        if (criteria.category && criteria.category !== 'all') {
            results = results.filter(item =>
                item.category === criteria.category || item.subcategory === criteria.category
            );
        }

        if (criteria.subcategory) {
            results = results.filter(item => item.subcategory === criteria.subcategory);
        }

        if (criteria.tags && criteria.tags.length > 0) {
            results = results.filter(item =>
                criteria.tags.some(tag => item.tags.includes(tag))
            );
        }

        if (criteria.type && criteria.type !== 'all') {
            results = results.filter(item => {
                if (criteria.type === 'video') {
                    return !item.type || item.type !== 'photo';
                } else if (criteria.type === 'photo') {
                    return item.type === 'photo';
                }
                return true;
            });
        }

        if (criteria.minRating) {
            results = results.filter(item => item.rating >= criteria.minRating);
        }

        if (criteria.sortBy) {
            results = this.sortResults(results, criteria.sortBy, criteria.sortOrder);
        }

        return results;
    }

    sortResults(results, sortBy, sortOrder = 'desc') {
        return results.sort((a, b) => {
            let aValue, bValue;

            switch (sortBy) {
                case 'rating':
                    aValue = a.rating;
                    bValue = b.rating;
                    break;
                case 'views':
                    aValue = a.views;
                    bValue = b.views;
                    break;
                case 'likes':
                    aValue = a.likes;
                    bValue = b.likes;
                    break;
                case 'date':
                    aValue = new Date(a.uploadDate);
                    bValue = new Date(b.uploadDate);
                    break;
                case 'title':
                    aValue = a.title.toLowerCase();
                    bValue = b.title.toLowerCase();
                    break;
                default:
                    return 0;
            }

            if (sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    // Get available categories and subcategories
    getAvailableCategories() {
        const categories = new Set();
        const subcategories = new Set();

        [...this.videos, ...this.newPhotos].forEach(item => {
            if (item.category) categories.add(item.category);
            if (item.subcategory) subcategories.add(item.subcategory);
        });

        return {
            categories: Array.from(categories),
            subcategories: Array.from(subcategories)
        };
    }

    // Get available tags
    getAvailableTags() {
        const tags = new Set();

        [...this.videos, ...this.newPhotos].forEach(item => {
            if (item.tags) {
                item.tags.forEach(tag => tags.add(tag));
            }
        });

        return Array.from(tags).sort();
    }

    setupEventListeners() {
        // Search functionality
        const searchForm = document.getElementById('searchForm');
        const searchInput = document.getElementById('searchInput');
        
        if (searchForm && searchInput) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch(searchInput.value.trim());
            });
            
            // Real-time search suggestions (optional)
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 2) {
                    this.showSearchSuggestions(query);
                }
            });
        }
        
        // Modal functionality
        this.setupModalListeners();
    }
    
    setupModalListeners() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
        });
        
        // Close modals with close button
        document.querySelectorAll('.modal-close').forEach(button => {
            button.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal);
            });
        });
        
        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    this.closeModal(activeModal);
                }
            }
        });
    }
    
    performSearch(query) {
        if (!query) return;
        
        const searchResults = this.videos.filter(video => 
            video.title.toLowerCase().includes(query.toLowerCase()) ||
            video.description.toLowerCase().includes(query.toLowerCase()) ||
            video.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase())) ||
            video.uploader.username.toLowerCase().includes(query.toLowerCase())
        );
        
        // Redirect to search results page or update current page
        this.displaySearchResults(searchResults, query);
    }
    
    displaySearchResults(results, query) {
        // For now, we'll update the current page if it has a videos grid
        const videosGrid = document.getElementById('featuredVideos') || 
                          document.getElementById('recentVideos') || 
                          document.getElementById('categoryVideos');
        
        if (videosGrid) {
            videosGrid.innerHTML = '';
            
            if (results.length === 0) {
                videosGrid.innerHTML = `
                    <div class="no-results">
                        <h3>No results found for "${query}"</h3>
                        <p>Try different keywords or browse our categories.</p>
                    </div>
                `;
                return;
            }
            
            results.forEach(video => {
                const videoCard = this.createVideoCard(video);
                videosGrid.appendChild(videoCard);
            });
            
            // Update page title if possible
            const sectionTitle = document.querySelector('.section-title');
            if (sectionTitle) {
                sectionTitle.textContent = `Search Results for "${query}" (${results.length})`;
            }
        }
    }
    
    createVideoCard(video) {
        const videoCard = document.createElement('a');
        videoCard.className = video.type === 'photo' ? 'photo-card' : 'video-card';

        // Handle different link destinations for photos vs videos
        if (video.type === 'photo') {
            videoCard.href = `#`;
            videoCard.onclick = (e) => {
                e.preventDefault();
                this.openPhotoModal(video);
            };
        } else {
            videoCard.href = `pages/video.html?id=${video.id}`;
        }

        // Get proper thumbnail with fallback
        const thumbnailSrc = this.getThumbnailSrc(video);
        const fallbackThumbnail = this.getDefaultThumbnail(video);

        videoCard.innerHTML = `
            <div class="video-thumbnail">
                <img src="${thumbnailSrc}" alt="${video.title}" loading="lazy"
                     onerror="this.onerror=null; this.src='${fallbackThumbnail}'"
                     data-video-id="${video.id}">
                <span class="video-duration">${video.duration}</span>
                ${video.type === 'photo' ? '<span class="photo-indicator">PHOTO</span>' : ''}
            </div>
            <div class="video-info">
                <h3 class="video-title">${video.title}</h3>
                <div class="video-meta">
                    <img src="${video.uploader.avatar}" alt="${video.uploader.username}" class="uploader-avatar">
                    <a href="#" class="uploader-name">${video.uploader.username}</a>
                </div>
                <div class="video-stats">
                    <span class="views">${this.formatNumber(video.views)} views</span>
                    <span class="likes">${this.formatNumber(video.likes)} likes</span>
                    <span class="rating">★ ${video.rating}</span>
                </div>
            </div>
        `;

        return videoCard;
    }

    getThumbnailSrc(video) {
        // For photos, always use the actual photo as the thumbnail
        if (video.type === 'photo') {
            // Use the photo URL directly as the thumbnail
            const photoUrl = video.videoUrl || video.thumbnail || video.url;
            return this.resolvePath(photoUrl);
        }

        // For videos, if video has a proper thumbnail (not file.svg), use it
        if (video.thumbnail && video.thumbnail !== '/file.svg' && !video.thumbnail.includes('file.svg')) {
            return this.resolvePath(video.thumbnail);
        }

        // Try to find existing thumbnail from video filename
        const videoUrl = video.videoUrl || video.url;
        if (videoUrl) {
            const videoFileName = videoUrl.split('/').pop();
            // Remove the file extension and add .jpg - handle all video formats
            const baseName = videoFileName.replace(/\.(ts|mp4|webm|avi|mov|mkv|flv|m4v|3gp|wmv)$/i, '');
            const potentialThumbnail = `categories/Thumbnails/${baseName}.jpg`;
            return this.resolvePath(potentialThumbnail);
        }

        // Fallback to default thumbnail
        return this.getDefaultThumbnail(video);
    }

    resolvePath(path) {
        if (!path) return path;

        // If already absolute or starts with http, return as is
        if (path.startsWith('http')) {
            return path;
        }

        // Handle paths that start with /
        if (path.startsWith('/')) {
            path = path.substring(1); // Remove leading slash
        }

        // Determine if we're in a subdirectory (like pages/)
        const currentPath = window.location.pathname;
        const isInSubdirectory = currentPath.includes('/pages/') || currentPath.split('/').length > 2;

        // Adjust path based on current directory level
        if (isInSubdirectory) {
            return `../${path}`;
        } else {
            return path;
        }
    }



    getDefaultThumbnail(video) {
        // For photos, try to use the actual photo URL first
        if (video.type === 'photo') {
            // If we have a photo URL, use it directly
            if (video.videoUrl || video.url) {
                return video.videoUrl || video.url;
            }
            return '/file.svg'; // Fallback for photos
        }

        // Try to generate a thumbnail using existing thumbnails as fallback
        const availableThumbnails = [
            'categories/Thumbnails/ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.jpg',
            'categories/Thumbnails/BLACKED Curvy Latina Dominated By BBC.jpg',
            'categories/Thumbnails/Stepsis Told Me to Take the Condom Off.jpg',
            'categories/Thumbnails/Waking Up Next to Perfect Booty Step Sister - Zoey Di Giacomo - Family Therapy - Alex Adams.jpg'
        ];

        // Use category-based assignment to existing thumbnails
        const categoryMappings = {
            'threesome': 0,
            'lesbian': 1,
            'stepsister': 2,
            'family': 3,
            'asian': 1,
            'romantic': 3,
            'general': 0
        };

        const thumbnailIndex = categoryMappings[video.subcategory] || 0;
        return this.resolvePath(availableThumbnails[thumbnailIndex]);
    }

    openPhotoModal(photo) {
        // Store current photo and get photo list for navigation
        this.currentPhoto = photo;
        this.photoList = this.newPhotos || [];
        this.currentPhotoIndex = this.photoList.findIndex(p => p.id === photo.id);

        // Create or update photo modal
        let photoModal = document.getElementById('photoModal');
        if (!photoModal) {
            photoModal = document.createElement('div');
            photoModal.id = 'photoModal';
            photoModal.className = 'modal photo-modal';
            photoModal.innerHTML = `
                <div class="modal-content">
                    <span class="close-modal">&times;</span>
                    <div class="photo-gallery-container">
                        <button class="nav-btn prev-btn" id="prevPhoto">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="photo-main">
                            <img id="modalPhoto" src="" alt="">
                            <div class="photo-controls">
                                <button class="photo-action-btn" id="downloadPhoto">
                                    <i class="fas fa-download"></i> Download
                                </button>
                                <button class="photo-action-btn" id="fullscreenPhoto">
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                                <button class="photo-action-btn" id="sharePhoto">
                                    <i class="fas fa-share"></i> Share
                                </button>
                            </div>
                        </div>
                        <button class="nav-btn next-btn" id="nextPhoto">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="photo-info">
                        <h2 id="modalPhotoTitle"></h2>
                        <p id="modalPhotoDescription"></p>
                        <div class="photo-meta">
                            <span id="modalPhotoUploader"></span>
                            <span id="modalPhotoDate"></span>
                            <span id="photoCounter"></span>
                        </div>
                        <div class="photo-stats">
                            <span id="modalPhotoViews"></span>
                            <span id="modalPhotoLikes"></span>
                            <span id="modalPhotoRating"></span>
                        </div>
                    </div>
                    <div class="photo-thumbnails" id="photoThumbnails">
                        <!-- Thumbnails will be loaded here -->
                    </div>
                </div>
            `;
            document.body.appendChild(photoModal);

            // Add event listeners
            this.setupPhotoModalListeners(photoModal);
        }

        // Update modal content
        this.updatePhotoModal();
        this.loadPhotoThumbnails();

        this.openModal('photoModal');
    }

    setupPhotoModalListeners(photoModal) {
        // Close functionality
        photoModal.querySelector('.close-modal').onclick = () => this.closeModal(photoModal);
        photoModal.onclick = (e) => {
            if (e.target === photoModal) this.closeModal(photoModal);
        };

        // Navigation buttons
        document.getElementById('prevPhoto').onclick = () => this.navigatePhoto(-1);
        document.getElementById('nextPhoto').onclick = () => this.navigatePhoto(1);

        // Action buttons
        document.getElementById('downloadPhoto').onclick = () => this.downloadPhoto();
        document.getElementById('fullscreenPhoto').onclick = () => this.openPhotoFullscreen();
        document.getElementById('sharePhoto').onclick = () => this.sharePhoto();

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (photoModal.classList.contains('active')) {
                switch(e.key) {
                    case 'ArrowLeft':
                        this.navigatePhoto(-1);
                        break;
                    case 'ArrowRight':
                        this.navigatePhoto(1);
                        break;
                    case 'Escape':
                        this.closeModal(photoModal);
                        break;
                }
            }
        });
    }

    updatePhotoModal() {
        const photo = this.currentPhoto;

        // Get the photo URL (could be in videoUrl, url, or thumbnail property)
        const photoUrl = photo.videoUrl || photo.url || photo.thumbnail;
        document.getElementById('modalPhoto').src = photoUrl;
        document.getElementById('modalPhotoTitle').textContent = photo.title;
        document.getElementById('modalPhotoDescription').textContent = photo.description;
        document.getElementById('modalPhotoUploader').textContent = `By ${photo.uploader.username}`;
        document.getElementById('modalPhotoDate').textContent = photo.uploadDate;
        document.getElementById('photoCounter').textContent = `${this.currentPhotoIndex + 1} of ${this.photoList.length}`;
        document.getElementById('modalPhotoViews').textContent = `${this.formatNumber(photo.views)} views`;
        document.getElementById('modalPhotoLikes').textContent = `${this.formatNumber(photo.likes)} likes`;
        document.getElementById('modalPhotoRating').textContent = `★ ${photo.rating}`;

        // Update navigation button states
        document.getElementById('prevPhoto').disabled = this.currentPhotoIndex === 0;
        document.getElementById('nextPhoto').disabled = this.currentPhotoIndex === this.photoList.length - 1;
    }

    navigatePhoto(direction) {
        const newIndex = this.currentPhotoIndex + direction;

        if (newIndex >= 0 && newIndex < this.photoList.length) {
            this.currentPhotoIndex = newIndex;
            this.currentPhoto = this.photoList[this.currentPhotoIndex];
            this.updatePhotoModal();
            this.updateThumbnailSelection();
        }
    }

    loadPhotoThumbnails() {
        const thumbnailContainer = document.getElementById('photoThumbnails');
        if (!thumbnailContainer) return;

        thumbnailContainer.innerHTML = '';

        this.photoList.forEach((photo, index) => {
            const thumbnail = document.createElement('div');
            thumbnail.className = `photo-thumbnail ${index === this.currentPhotoIndex ? 'active' : ''}`;

            // Get the photo thumbnail URL (could be in thumbnail, videoUrl, or url property)
            const thumbnailUrl = photo.thumbnail || photo.videoUrl || photo.url;

            thumbnail.innerHTML = `
                <img src="${thumbnailUrl}" alt="${photo.title}">
                <span class="thumbnail-title">${photo.title}</span>
            `;

            thumbnail.onclick = () => {
                this.currentPhotoIndex = index;
                this.currentPhoto = photo;
                this.updatePhotoModal();
                this.updateThumbnailSelection();
            };

            thumbnailContainer.appendChild(thumbnail);
        });
    }

    updateThumbnailSelection() {
        const thumbnails = document.querySelectorAll('.photo-thumbnail');
        thumbnails.forEach((thumb, index) => {
            thumb.classList.toggle('active', index === this.currentPhotoIndex);
        });
    }

    downloadPhoto() {
        const photo = this.currentPhoto;
        const link = document.createElement('a');
        link.href = photo.videoUrl;
        link.download = `${photo.title}.${this.getFileExtension(photo.videoUrl)}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    openPhotoFullscreen() {
        const photo = this.currentPhoto;
        const fullscreenModal = document.createElement('div');
        fullscreenModal.className = 'fullscreen-photo-modal';
        fullscreenModal.innerHTML = `
            <div class="fullscreen-content">
                <button class="fullscreen-close">&times;</button>
                <img src="${photo.videoUrl}" alt="${photo.title}">
            </div>
        `;

        document.body.appendChild(fullscreenModal);

        // Close functionality
        fullscreenModal.querySelector('.fullscreen-close').onclick = () => {
            document.body.removeChild(fullscreenModal);
        };

        fullscreenModal.onclick = (e) => {
            if (e.target === fullscreenModal) {
                document.body.removeChild(fullscreenModal);
            }
        };
    }

    sharePhoto() {
        const photo = this.currentPhoto;
        const shareUrl = `${window.location.origin}${window.location.pathname}?photo=${photo.id}`;

        if (navigator.share) {
            navigator.share({
                title: photo.title,
                text: photo.description,
                url: shareUrl
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showSuccessMessage('Photo link copied to clipboard!');
            });
        }
    }
    
    createCategoryCard(category) {
        const categoryCard = document.createElement('a');
        categoryCard.className = 'category-card';
        categoryCard.href = `pages/category.html?category=${category.id}`;
        
        categoryCard.innerHTML = `
            <div class="category-icon">${category.icon}</div>
            <h3 class="category-name">${category.name}</h3>
            <p class="category-description">${category.description}</p>
        `;
        
        return categoryCard;
    }

    showSuccessMessage(message) {
        // Create success notification
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.textContent = message;

        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Hide and remove notification
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    getFileExtension(url) {
        return url.split('.').pop().toLowerCase();
    }

    // Utility functions
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} month${months > 1 ? 's' : ''} ago`;
        } else {
            const years = Math.floor(diffDays / 365);
            return `${years} year${years > 1 ? 's' : ''} ago`;
        }
    }
    
    // LocalStorage functions
    getFavorites() {
        const favorites = localStorage.getItem('pornTubeX_favorites');
        return favorites ? JSON.parse(favorites) : [];
    }
    
    saveFavorites() {
        localStorage.setItem('pornTubeX_favorites', JSON.stringify(this.favorites));
    }
    
    getLikes() {
        const likes = localStorage.getItem('pornTubeX_likes');
        return likes ? JSON.parse(likes) : [];
    }
    
    saveLikes() {
        localStorage.setItem('pornTubeX_likes', JSON.stringify(this.likes));
    }
    
    toggleFavorite(videoId) {
        const index = this.favorites.indexOf(videoId);
        if (index > -1) {
            this.favorites.splice(index, 1);
        } else {
            this.favorites.push(videoId);
        }
        this.saveFavorites();
        return this.favorites.includes(videoId);
    }
    
    toggleLike(videoId) {
        const index = this.likes.indexOf(videoId);
        if (index > -1) {
            this.likes.splice(index, 1);
        } else {
            this.likes.push(videoId);
        }
        this.saveLikes();
        return this.likes.includes(videoId);
    }
    
    // Modal functions
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    // Error handling
    showError(message) {
        console.error(message);
        // You could implement a toast notification system here
        alert(message);
    }
    
    // Get video by ID
    getVideoById(id) {
        return this.videos.find(video => video.id === id);
    }
    
    // Get user by ID
    getUserById(id) {
        return this.users.find(user => user.id === id);
    }
    
    // Get comments for video
    getCommentsForVideo(videoId) {
        return this.comments.filter(comment => comment.videoId === videoId);
    }
    
    // Get category by ID
    getCategoryById(id) {
        return this.categories.find(category => category.id === id);
    }
    
    // Get videos by category
    getVideosByCategory(categoryId) {
        return this.videos.filter(video => video.category === categoryId);
    }
    
    // Get related videos (by tags and category)
    getRelatedVideos(video, limit = 6) {
        const related = this.videos.filter(v => {
            if (v.id === video.id) return false;
            
            // Check if videos share tags or category
            const sharedTags = v.tags.some(tag => video.tags.includes(tag));
            const sameCategory = v.category === video.category;
            
            return sharedTags || sameCategory;
        });
        
        // Sort by relevance (more shared tags = higher relevance)
        related.sort((a, b) => {
            const aSharedTags = a.tags.filter(tag => video.tags.includes(tag)).length;
            const bSharedTags = b.tags.filter(tag => video.tags.includes(tag)).length;
            return bSharedTags - aSharedTags;
        });
        
        return related.slice(0, limit);
    }
}

// Initialize the application
let app;
document.addEventListener('DOMContentLoaded', () => {
    console.log('PornTubeX: DOM loaded, creating app instance...');
    app = new PornTubeX();
    window.app = app; // Make app globally accessible
    console.log('PornTubeX: App instance created and assigned to window.app');
});
