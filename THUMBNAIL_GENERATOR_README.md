# Video Thumbnail Generator

A Python script that extracts random frames from video files and saves them as image thumbnails.

## Features

- **Multiple Video Formats**: Supports .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .ts, .m4v
- **Random Frame Extraction**: Extracts frames from random timestamps (avoiding first/last 5% of video)
- **Batch Processing**: Processes multiple video files automatically
- **Error Handling**: Comprehensive error handling for corrupted or unsupported files
- **Image Enhancement**: Applies basic contrast and sharpness improvements
- **Duplicate Prevention**: Skips videos that already have thumbnails
- **Detailed Logging**: Logs all operations to file and console

## Requirements

- Python 3.7 or higher
- OpenCV (cv2)
- Pillow (PIL)

## Installation

1. **Install Python** (if not already installed):
   - Download from [python.org](https://python.org)
   - Make sure to check "Add Python to PATH" during installation

2. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Quick Start (Windows)
Double-click `generate_thumbnails.bat` to automatically:
- Install dependencies
- Generate thumbnails for all videos in `categories/Videos`
- Save thumbnails to `categories/Thumbnails`

### Command Line Usage

#### Basic usage (uses default folders):
```bash
python video_thumbnail_generator.py
```

#### Specify custom input and output folders:
```bash
python video_thumbnail_generator.py "path/to/videos" "path/to/thumbnails"
```

#### Command line options:
```bash
python video_thumbnail_generator.py --help
```

### Examples

1. **Process videos in default location**:
   ```bash
   python video_thumbnail_generator.py
   ```

2. **Process videos from custom folder**:
   ```bash
   python video_thumbnail_generator.py "C:/MyVideos" "C:/MyThumbnails"
   ```

3. **Overwrite existing thumbnails**:
   ```bash
   python video_thumbnail_generator.py --overwrite
   ```

## How It Works

1. **Scans** the input folder for supported video files
2. **Analyzes** each video to get frame count and duration
3. **Selects** a random frame (avoiding first/last 5% of video)
4. **Extracts** the frame using OpenCV
5. **Enhances** the image quality (contrast, sharpness)
6. **Saves** as JPEG thumbnail with 85% quality

## Output

- **Format**: JPEG (.jpg)
- **Quality**: 85% (optimized for file size vs quality)
- **Naming**: Same as video filename with .jpg extension
- **Location**: `categories/Thumbnails` (or specified output folder)

## Error Handling

The script handles various error conditions:
- **Corrupted videos**: Skips and logs error
- **Unsupported formats**: Ignores non-video files
- **Permission issues**: Reports access problems
- **Missing folders**: Creates output folder automatically
- **Invalid video properties**: Skips videos with no frames or invalid duration

## Logging

All operations are logged to:
- **Console**: Real-time progress updates
- **File**: `thumbnail_generator.log` (detailed log with timestamps)

## Supported Video Formats

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WMV (.wmv)
- FLV (.flv)
- WebM (.webm)
- Transport Stream (.ts)
- M4V (.m4v)

## Configuration

You can modify these settings in the script:

```python
# Supported video formats
SUPPORTED_FORMATS = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.ts', '.m4v'}

# Output image format and quality
OUTPUT_FORMAT = '.jpg'
OUTPUT_QUALITY = 85  # JPEG quality (1-100)
```

## Troubleshooting

### Common Issues

1. **"Python is not recognized"**:
   - Install Python and add it to PATH
   - Restart command prompt after installation

2. **"No module named cv2"**:
   - Run: `pip install opencv-python`

3. **"Permission denied"**:
   - Run as administrator
   - Check folder permissions

4. **"No video files found"**:
   - Verify input folder path
   - Check if videos are in supported formats

5. **Poor thumbnail quality**:
   - Increase `OUTPUT_QUALITY` value (up to 100)
   - Videos with low resolution will produce low-quality thumbnails

### Performance Tips

- **Large video collections**: The script processes videos sequentially. For very large collections, consider running on smaller batches.
- **Network drives**: Processing videos on network drives may be slower.
- **SSD vs HDD**: Faster storage improves processing speed.

## License

This script is provided as-is for personal and educational use.
