@echo off
echo Testing FFmpeg installation...
echo.

REM Test if ffmpeg is in PATH
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: FFmpeg is working and accessible via PATH!
    echo.
    echo FFmpeg version:
    ffmpeg -version | findstr "ffmpeg version"
) else (
    echo FFmpeg not found in PATH. Trying direct path...
    
    REM Try the winget installation path
    set "FFMPEG_PATH=C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe"
    
    if exist "%FFMPEG_PATH%" (
        echo SUCCESS: FFmpeg found at winget installation path!
        echo.
        echo FFmpeg version:
        "%FFMPEG_PATH%" -version | findstr "ffmpeg version"
        echo.
        echo Adding to current session PATH...
        set "PATH=%PATH%;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin"
        echo.
        echo Testing again...
        ffmpeg -version >nul 2>&1
        if %errorlevel% equ 0 (
            echo SUCCESS: FFmpeg is now working in current session!
        ) else (
            echo Note: You may need to restart your command prompt for PATH changes to take effect.
        )
    ) else (
        echo ERROR: FFmpeg not found at expected location.
        echo Please restart your command prompt and try again.
    )
)

echo.
pause
