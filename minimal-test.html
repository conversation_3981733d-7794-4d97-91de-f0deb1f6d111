<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test</title>
    <link rel="stylesheet" href="assets/css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Minimal Test</h1>
        <p>Testing if the main website components work with minimal setup.</p>
        
        <div id="status" style="padding: 20px; background: #333; margin: 20px 0; border-radius: 5px;">
            Loading...
        </div>
        
        <!-- Featured Videos -->
        <section class="featured-section">
            <div class="container">
                <h3 class="section-title">Featured Videos</h3>
                <div class="videos-grid" id="featuredVideos">
                    <!-- Featured videos will be loaded dynamically -->
                </div>
            </div>
        </section>

        <!-- Photos Section -->
        <section class="photos-section">
            <div class="container">
                <h3 class="section-title">Photo Gallery</h3>
                <div class="photos-grid" id="photosGrid">
                    <!-- Photos will be loaded dynamically -->
                </div>
            </div>
        </section>
    </div>

    <script>
        // Simple test to see if the main.js and homepage.js work
        let statusElement = document.getElementById('status');
        let checkCount = 0;
        
        function updateStatus(message) {
            statusElement.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.log(message);
        }
        
        function checkApp() {
            checkCount++;
            
            if (window.app) {
                if (window.app.videos && window.app.videos.length > 0) {
                    updateStatus(`✅ App loaded successfully with ${window.app.videos.length} videos`);
                    
                    // Manually trigger homepage loading
                    setTimeout(() => {
                        const featuredVideos = document.getElementById('featuredVideos');
                        const photosGrid = document.getElementById('photosGrid');
                        
                        if (featuredVideos.children.length > 0 || photosGrid.children.length > 0) {
                            updateStatus(`✅ Content loaded! Featured: ${featuredVideos.children.length}, Photos: ${photosGrid.children.length}`);
                        } else {
                            updateStatus(`⚠️ App loaded but no content displayed yet...`);
                            
                            // Try to manually create some content
                            const sampleVideos = window.app.videos.slice(0, 3);
                            sampleVideos.forEach(video => {
                                const card = window.app.createVideoCard(video);
                                featuredVideos.appendChild(card);
                            });
                            
                            updateStatus(`✅ Manually added ${sampleVideos.length} video cards`);
                        }
                    }, 1000);
                    
                } else {
                    updateStatus(`⚠️ App exists but no videos loaded yet (attempt ${checkCount})`);
                    if (checkCount < 50) {
                        setTimeout(checkApp, 200);
                    } else {
                        updateStatus(`❌ Timeout: App never loaded videos`);
                    }
                }
            } else {
                updateStatus(`⚠️ Waiting for app to be created (attempt ${checkCount})`);
                if (checkCount < 50) {
                    setTimeout(checkApp, 200);
                } else {
                    updateStatus(`❌ Timeout: App never created`);
                }
            }
        }
        
        // Start checking after DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('DOM loaded, starting app check...');
            setTimeout(checkApp, 500);
        });
    </script>

    <!-- Load the main scripts -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/homepage.js"></script>
</body>
</html>
