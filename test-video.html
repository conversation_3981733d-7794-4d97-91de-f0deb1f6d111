<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player Test - PornTubeX</title>
    <style>
        body { background: #000; color: white; font-family: Arial; padding: 20px; }
        video { width: 100%; max-width: 800px; }
        .video-info { margin: 20px 0; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #2d5016; border: 1px solid #4caf50; }
        .error { background: #5d1a1a; border: 1px solid #f44336; }
        .info { background: #1a4d5d; border: 1px solid #2196f3; }
    </style>
</head>
<body>
    <h1>PornTubeX Video Player Test</h1>
    
    <div class="test-result info">
        <h3>Testing Video Playback</h3>
        <p>This page tests if videos can be loaded and played properly.</p>
    </div>

    <div class="video-info">
        <h2>Test Video 1: <PERSON> (MP4)</h2>
        <video controls poster="categories/Thumbnails/ANGELA WHITE - Hot Threesome with <PERSON> and <PERSON> - Pornhub.com.jpg">
            <source src="categories/Videos/ANGELA WHITE - Hot Threesome with <PERSON> <PERSON> and Johnny Sins - Pornhub.com.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p>File: ANGELA WHITE - Hot Threesome with Lena Paul and Johnny Sins - Pornhub.com.mp4</p>
    </div>

    <div class="video-info">
        <h2>Test Video 2: Sensual Massage (TS)</h2>
        <video controls poster="categories/Thumbnails/Submissive Receives more than just an Oily Massage. - Pornhub.com.jpg">
            <source src="categories/Videos/Submissive Receives more than just an Oily Massage. - Pornhub.com.ts" type="video/mp2t">
            Your browser does not support the video tag.
        </video>
        <p>File: Submissive Receives more than just an Oily Massage. - Pornhub.com.ts</p>
    </div>

    <div class="video-info">
        <h2>Test Video 3: Passionate Romance (MP4)</h2>
        <video controls poster="categories/Thumbnails/【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6 - Pornhub.com.jpg">
            <source src="categories/Videos/【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6 - Pornhub.com.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
        <p>File: 【Mr.Bunny】TZ-112 Mr.Bunny's Special Massage Club EP6 - Pornhub.com.mp4</p>
    </div>

    <script>
        // Test video loading
        const videos = document.querySelectorAll('video');
        videos.forEach((video, index) => {
            const resultDiv = document.createElement('div');
            
            video.addEventListener('loadstart', () => {
                resultDiv.className = 'test-result info';
                resultDiv.innerHTML = `<p>Video ${index + 1}: Loading started...</p>`;
                video.parentNode.appendChild(resultDiv);
            });
            
            video.addEventListener('canplay', () => {
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = `<p>✓ Video ${index + 1}: Successfully loaded and ready to play!</p>`;
            });
            
            video.addEventListener('error', (e) => {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `<p>✗ Video ${index + 1}: Error loading video - ${e.target.error?.message || 'Unknown error'}</p>`;
            });
        });
    </script>
</body>
</html>
